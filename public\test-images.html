<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SMS Gateway Images Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .image-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .image-section h2 {
            color: #333;
            margin-bottom: 10px;
        }
        .image-section img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SMS Gateway Images Test</h1>
        <p>This page tests if all SMS gateway instructional images are loading correctly.</p>
        
        <div class="image-section">
            <h2>1. Base URL and Parameters</h2>
            <p>Shows API documentation for base URL and messaging parameters</p>
            <img src="storage/images/base_url_and_params.svg" alt="Base URL and Parameters" 
                 onload="showStatus('base-status', true)" 
                 onerror="showStatus('base-status', false)">
            <div id="base-status" class="status">Loading...</div>
        </div>
        
        <div class="image-section">
            <h2>2. API Key and Token</h2>
            <p>Shows SMS gateway API credentials interface with Account SID and Auth Token</p>
            <img src="storage/images/api_key_and_token.svg" alt="API Key and Token" 
                 onload="showStatus('api-status', true)" 
                 onerror="showStatus('api-status', false)">
            <div id="api-status" class="status">Loading...</div>
        </div>
        
        <div class="image-section">
            <h2>3. SMS Gateway Settings 1</h2>
            <p>Shows admin panel SMS gateway configuration form</p>
            <img src="storage/images/sms_gateway_1.svg" alt="SMS Gateway Settings 1" 
                 onload="showStatus('gateway1-status', true)" 
                 onerror="showStatus('gateway1-status', false)">
            <div id="gateway1-status" class="status">Loading...</div>
        </div>
        
        <div class="image-section">
            <h2>4. SMS Gateway Settings 2</h2>
            <p>Shows advanced SMS gateway settings with message templates and delivery options</p>
            <img src="storage/images/sms_gateway_2.svg" alt="SMS Gateway Settings 2" 
                 onload="showStatus('gateway2-status', true)" 
                 onerror="showStatus('gateway2-status', false)">
            <div id="gateway2-status" class="status">Loading...</div>
        </div>
    </div>

    <script>
        function showStatus(elementId, success) {
            const element = document.getElementById(elementId);
            if (success) {
                element.className = 'status success';
                element.textContent = '✓ Image loaded successfully';
            } else {
                element.className = 'status error';
                element.textContent = '✗ Failed to load image';
            }
        }
        
        // Set initial loading status
        setTimeout(() => {
            const statusElements = document.querySelectorAll('.status');
            statusElements.forEach(element => {
                if (element.textContent === 'Loading...') {
                    element.className = 'status error';
                    element.textContent = '✗ Image failed to load (timeout)';
                }
            });
        }, 5000);
    </script>
</body>
</html>
