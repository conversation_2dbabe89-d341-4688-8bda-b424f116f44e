<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
<defs>
<style>
.modern-gradient { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.text-modern { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
</style>
</defs>

  <defs>
    <linearGradient id="profileGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e5e7eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d1d5db;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="userGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9ca3af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6b7280;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#profileGradient)" rx="8"/>
  <circle cx="150" cy="80" r="25" fill="url(#userGradient)"/>
  <path d="M120 140 Q120 120 150 120 Q180 120 180 140 L180 160 L120 160 Z" fill="url(#userGradient)"/>
  <text x="150" y="190" text-anchor="middle" font-family="Segoe UI" font-size="12" font-weight="500" fill="#6b7280">No Image</text>
</svg>