<?php

namespace App\Http\Controllers;

use App\Models\GeofenceZone;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class GeofenceController extends Controller
{
    protected $user;
    protected $workspace;

    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            $this->user = Auth::user();
            $this->workspace = $this->user->workspace;
            return $next($request);
        });
    }

    /**
     * Display geofence zones management
     */
    public function index()
    {
        // Check if user has admin access
        if (!$this->user->hasRole(['admin', 'manager'])) {
            abort(403, 'Unauthorized access to geofence management.');
        }

        return view('geofence.index');
    }

    /**
     * Get all geofence zones
     */
    public function getZones(Request $request)
    {
        try {
            // Check if user has admin access
            if (!$this->user->hasRole(['admin', 'manager'])) {
                return response()->json([
                    'error' => true,
                    'message' => 'Unauthorized access.'
                ], 403);
            }

            $zones = GeofenceZone::where('workspace_id', $this->workspace->id)
                ->with('creator:id,first_name,last_name')
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'error' => false,
                'zones' => $zones->map(function($zone) {
                    return [
                        'id' => $zone->id,
                        'name' => $zone->name,
                        'description' => $zone->description,
                        'zone_type' => $zone->zone_type,
                        'zone_purpose' => $zone->zone_purpose,
                        'center_coordinates' => $zone->center_coordinates,
                        'radius' => $zone->radius,
                        'polygon_coordinates' => $zone->polygon_coordinates,
                        'is_active' => $zone->is_active,
                        'alert_type' => $zone->alert_type,
                        'entry_count' => $zone->entry_count,
                        'exit_count' => $zone->exit_count,
                        'last_activity' => $zone->last_activity?->format('M d, Y h:i A'),
                        'created_by' => $zone->creator->first_name . ' ' . $zone->creator->last_name,
                        'created_at' => $zone->created_at->format('M d, Y h:i A'),
                        'formatted_description' => $zone->formatted_description,
                    ];
                })
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => 'Failed to load geofence zones: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a new geofence zone
     */
    public function store(Request $request)
    {
        try {
            // Check if user has admin access
            if (!$this->user->hasRole(['admin', 'manager'])) {
                return response()->json([
                    'error' => true,
                    'message' => 'Unauthorized access.'
                ], 403);
            }

            $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'zone_type' => ['required', Rule::in(GeofenceZone::ZONE_TYPES)],
                'center_latitude' => 'required|numeric|between:-90,90',
                'center_longitude' => 'required|numeric|between:-180,180',
                'radius' => 'required_if:zone_type,circle|nullable|numeric|min:1',
                'polygon_coordinates' => 'required_if:zone_type,polygon,rectangle|nullable|array',
                'zone_purpose' => ['required', Rule::in(GeofenceZone::ZONE_PURPOSES)],
                'alert_type' => ['required', Rule::in(GeofenceZone::ALERT_TYPES)],
                'dwell_time_threshold' => 'nullable|integer|min:1',
                'notify_managers' => 'boolean',
                'notify_admins' => 'boolean',
                'notify_user' => 'boolean',
                'active_hours' => 'nullable|array',
                'active_days' => 'nullable|array',
                'respect_working_hours' => 'boolean',
            ]);

            $zone = GeofenceZone::create([
                'name' => $request->name,
                'description' => $request->description,
                'workspace_id' => $this->workspace->id,
                'created_by' => $this->user->id,
                'zone_type' => $request->zone_type,
                'center_latitude' => $request->center_latitude,
                'center_longitude' => $request->center_longitude,
                'radius' => $request->radius,
                'polygon_coordinates' => $request->polygon_coordinates,
                'zone_purpose' => $request->zone_purpose,
                'alert_type' => $request->alert_type,
                'dwell_time_threshold' => $request->dwell_time_threshold,
                'notify_managers' => $request->boolean('notify_managers'),
                'notify_admins' => $request->boolean('notify_admins'),
                'notify_user' => $request->boolean('notify_user'),
                'active_hours' => $request->active_hours,
                'active_days' => $request->active_days,
                'respect_working_hours' => $request->boolean('respect_working_hours'),
                'is_active' => true,
            ]);

            return response()->json([
                'error' => false,
                'message' => 'Geofence zone created successfully',
                'zone' => $zone
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => 'Failed to create geofence zone: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a geofence zone
     */
    public function update(Request $request, GeofenceZone $zone)
    {
        try {
            // Check if user has admin access and zone belongs to workspace
            if (!$this->user->hasRole(['admin', 'manager']) || $zone->workspace_id !== $this->workspace->id) {
                return response()->json([
                    'error' => true,
                    'message' => 'Unauthorized access.'
                ], 403);
            }

            $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'zone_type' => ['required', Rule::in(GeofenceZone::ZONE_TYPES)],
                'center_latitude' => 'required|numeric|between:-90,90',
                'center_longitude' => 'required|numeric|between:-180,180',
                'radius' => 'required_if:zone_type,circle|nullable|numeric|min:1',
                'polygon_coordinates' => 'required_if:zone_type,polygon,rectangle|nullable|array',
                'zone_purpose' => ['required', Rule::in(GeofenceZone::ZONE_PURPOSES)],
                'alert_type' => ['required', Rule::in(GeofenceZone::ALERT_TYPES)],
                'dwell_time_threshold' => 'nullable|integer|min:1',
                'notify_managers' => 'boolean',
                'notify_admins' => 'boolean',
                'notify_user' => 'boolean',
                'active_hours' => 'nullable|array',
                'active_days' => 'nullable|array',
                'respect_working_hours' => 'boolean',
                'is_active' => 'boolean',
            ]);

            $zone->update($request->all());

            return response()->json([
                'error' => false,
                'message' => 'Geofence zone updated successfully',
                'zone' => $zone
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => 'Failed to update geofence zone: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a geofence zone
     */
    public function destroy(GeofenceZone $zone)
    {
        try {
            // Check if user has admin access and zone belongs to workspace
            if (!$this->user->hasRole(['admin', 'manager']) || $zone->workspace_id !== $this->workspace->id) {
                return response()->json([
                    'error' => true,
                    'message' => 'Unauthorized access.'
                ], 403);
            }

            $zone->delete();

            return response()->json([
                'error' => false,
                'message' => 'Geofence zone deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => 'Failed to delete geofence zone: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle zone active status
     */
    public function toggleActive(GeofenceZone $zone)
    {
        try {
            // Check if user has admin access and zone belongs to workspace
            if (!$this->user->hasRole(['admin', 'manager']) || $zone->workspace_id !== $this->workspace->id) {
                return response()->json([
                    'error' => true,
                    'message' => 'Unauthorized access.'
                ], 403);
            }

            $zone->update(['is_active' => !$zone->is_active]);

            return response()->json([
                'error' => false,
                'message' => 'Geofence zone status updated successfully',
                'is_active' => $zone->is_active
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => 'Failed to update zone status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get geofence activity log
     */
    public function getActivity(Request $request)
    {
        try {
            // Check if user has admin access
            if (!$this->user->hasRole(['admin', 'manager'])) {
                return response()->json([
                    'error' => true,
                    'message' => 'Unauthorized access.'
                ], 403);
            }

            $startDate = $request->get('start_date', now()->subDays(7)->toDateString());
            $endDate = $request->get('end_date', now()->toDateString());
            $zoneId = $request->get('zone_id');

            // Get users in the current workspace
            $workspaceUserIds = \DB::table('user_workspace')
                ->where('workspace_id', $this->workspace->id)
                ->pluck('user_id')
                ->toArray();

            $query = \App\Models\TaskLocation::whereNotNull('geofence_event')
                ->whereIn('user_id', $workspaceUserIds)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->with(['user:id,first_name,last_name', 'task:id,title']);

            if ($zoneId) {
                $query->whereJsonContains('geofence_zones', (int)$zoneId);
            }

            $activities = $query->orderBy('created_at', 'desc')
                ->limit(100)
                ->get();

            return response()->json([
                'error' => false,
                'activities' => $activities->map(function($activity) {
                    return [
                        'id' => $activity->id,
                        'user_name' => $activity->user->first_name . ' ' . $activity->user->last_name,
                        'task_title' => $activity->task->title,
                        'event_type' => $activity->geofence_event,
                        'coordinates' => $activity->coordinates,
                        'address' => $activity->formatted_address,
                        'timestamp' => $activity->created_at->format('M d, Y h:i A'),
                        'zones' => $activity->geofence_zones,
                    ];
                })
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => 'Failed to load geofence activity: ' . $e->getMessage()
            ], 500);
        }
    }
}
