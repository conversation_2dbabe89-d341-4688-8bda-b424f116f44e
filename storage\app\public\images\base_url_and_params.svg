<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="600" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="600" fill="#f1f5f9"/>
  
  <!-- Header -->
  <rect width="800" height="80" fill="url(#headerGradient)"/>
  <text x="40" y="35" font-family="Segoe UI, Arial, sans-serif" font-size="24" font-weight="600" fill="white">SMS Gateway API Documentation</text>
  <text x="40" y="60" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="rgba(255,255,255,0.8)">Base URL and Parameters Configuration</text>
  
  <!-- Navigation -->
  <rect x="0" y="80" width="200" height="520" fill="#1e293b"/>
  <text x="20" y="110" font-family="Segoe UI, Arial, sans-serif" font-size="16" font-weight="500" fill="white">API Reference</text>
  <rect x="10" y="130" width="180" height="35" rx="4" fill="#334155"/>
  <text x="20" y="150" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="white">📖 Overview</text>
  <rect x="10" y="175" width="180" height="35" rx="4" fill="#3b82f6"/>
  <text x="20" y="195" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="white">🔗 Endpoints</text>
  <text x="20" y="230" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#94a3b8">🔐 Authentication</text>
  <text x="20" y="260" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#94a3b8">📝 Examples</text>
  <text x="20" y="290" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#94a3b8">❓ Support</text>
  
  <!-- Main Content Area -->
  <rect x="220" y="100" width="560" height="480" fill="white" filter="url(#shadow)" rx="8"/>
  
  <!-- Page Title -->
  <text x="240" y="140" font-family="Segoe UI, Arial, sans-serif" font-size="28" font-weight="700" fill="#1a202c">API Endpoints</text>
  <text x="240" y="165" font-family="Segoe UI, Arial, sans-serif" font-size="16" fill="#718096">Configure your base URL and messaging parameters</text>
  
  <!-- Base URL Section -->
  <rect x="240" y="190" width="520" height="120" fill="url(#cardGradient)" stroke="#e2e8f0" stroke-width="1" rx="8"/>
  <text x="260" y="220" font-family="Segoe UI, Arial, sans-serif" font-size="18" font-weight="600" fill="#2d3748">Base URL Configuration</text>
  <text x="260" y="240" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#718096">Primary endpoint for all SMS operations</text>
  
  <rect x="260" y="250" width="480" height="40" fill="#f7fafc" stroke="#cbd5e0" stroke-width="1" rx="4"/>
  <text x="270" y="272" font-family="Monaco, Consolas, monospace" font-size="14" fill="#4a5568">https://api.smsgateway.com/v1</text>
  <rect x="680" y="255" width="60" height="30" fill="#3b82f6" rx="4"/>
  <text x="695" y="272" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="500" fill="white">Copy</text>
  
  <!-- Messaging Endpoint -->
  <rect x="240" y="330" width="520" height="160" fill="url(#cardGradient)" stroke="#e2e8f0" stroke-width="1" rx="8"/>
  <text x="260" y="360" font-family="Segoe UI, Arial, sans-serif" font-size="18" font-weight="600" fill="#2d3748">Send SMS Endpoint</text>
  <text x="260" y="380" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#718096">POST endpoint for sending SMS messages</text>
  
  <rect x="260" y="390" width="480" height="40" fill="#f7fafc" stroke="#cbd5e0" stroke-width="1" rx="4"/>
  <text x="270" y="412" font-family="Monaco, Consolas, monospace" font-size="14" fill="#4a5568">POST /messages</text>
  
  <text x="260" y="450" font-family="Segoe UI, Arial, sans-serif" font-size="14" font-weight="600" fill="#2d3748">Required Parameters:</text>
  <text x="260" y="470" font-family="Monaco, Consolas, monospace" font-size="12" fill="#4a5568">• to: Recipient phone number (+1234567890)</text>
  <text x="260" y="485" font-family="Monaco, Consolas, monospace" font-size="12" fill="#4a5568">• from: Sender phone number</text>
  <text x="260" y="500" font-family="Monaco, Consolas, monospace" font-size="12" fill="#4a5568">• body: Message content (max 160 chars)</text>
  
  <!-- Example Request -->
  <rect x="240" y="510" width="520" height="60" fill="#1e293b" stroke="#334155" stroke-width="1" rx="6"/>
  <text x="260" y="530" font-family="Segoe UI, Arial, sans-serif" font-size="14" font-weight="600" fill="white">Example Request</text>
  <text x="260" y="550" font-family="Monaco, Consolas, monospace" font-size="12" fill="#94a3b8">curl -X POST https://api.smsgateway.com/v1/messages \</text>
  <text x="260" y="565" font-family="Monaco, Consolas, monospace" font-size="12" fill="#94a3b8">  -H "Authorization: Bearer YOUR_API_KEY"</text>
  
  <!-- Status Indicators -->
  <circle cx="720" cy="200" r="6" fill="#10b981"/>
  <text x="735" y="205" font-family="Segoe UI, Arial, sans-serif" font-size="12" fill="#10b981">Available</text>
  
  <circle cx="720" cy="340" r="6" fill="#10b981"/>
  <text x="735" y="345" font-family="Segoe UI, Arial, sans-serif" font-size="12" fill="#10b981">Available</text>
</svg>
