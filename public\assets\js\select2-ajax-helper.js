"use strict";

/**
 * Initialize Select2 with AJAX functionality
 * @param {string} elementId - The selector for the element
 * @param {string} ajaxUrl - The URL for AJAX requests
 * @param {string} placeholderText - Placeholder text for the dropdown
 * @param {boolean} allowClear - Whether to allow clearing the selection
 * @param {number} minimumInputLength - Minimum input length before search
 * @param {boolean} initialData - Whether to load initial data
 * @param {function} extraData - Function to return extra data for AJAX requests
 */
function initSelect2Ajax(
    elementId,
    ajaxUrl,
    placeholderText = "Please select...",
    allowClear = true,
    minimumInputLength = 0,
    initialData = false,
    extraData = () => ({})
) {
    const $element = $(elementId);
    if (!$element.length) {
        console.warn(`Element ${elementId} not found for Select2 initialization`);
        return;
    }

    const $modalParent = $element.closest(".modal");
    
    const select2Config = {
        placeholder: placeholderText,
        width: "100%",
        allowClear: allowClear,
        minimumInputLength: minimumInputLength,
        dropdownParent: $modalParent.length ? $modalParent : $(document.body),
        ajax: {
            url: ajaxUrl,
            dataType: "json",
            delay: 250,
            data: function (params) {
                const baseData = {
                    q: params.term || "",
                    page: params.page || 1
                };
                
                // Merge with extra data if provided
                if (typeof extraData === 'function') {
                    return { ...baseData, ...extraData() };
                }
                
                return baseData;
            },
            processResults: function (data, params) {
                params.page = params.page || 1;
                
                return {
                    results: data.results || data.data || [],
                    pagination: {
                        more: data.pagination ? data.pagination.more : false
                    }
                };
            },
            cache: true
        },
        escapeMarkup: function (markup) {
            return markup;
        },
        templateResult: function (item) {
            if (item.loading) {
                return item.text;
            }
            return item.text || item.name || item.title;
        },
        templateSelection: function (item) {
            return item.text || item.name || item.title;
        }
    };

    // Initialize Select2
    $element.select2(select2Config);

    // Load initial data if requested
    if (initialData && $element.val()) {
        // Trigger a change to load initial selection
        $element.trigger('change');
    }
}
