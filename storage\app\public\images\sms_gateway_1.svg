<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="600" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="600" fill="#f8fafc"/>
  
  <!-- Header -->
  <rect width="800" height="70" fill="url(#headerGradient)"/>
  <text x="30" y="30" font-family="Segoe UI, Arial, sans-serif" font-size="22" font-weight="600" fill="white">Admin Panel - SMS Gateway Settings</text>
  <text x="30" y="50" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="rgba(255,255,255,0.8)">Configure your SMS gateway integration</text>
  
  <!-- Breadcrumb -->
  <rect x="0" y="70" width="800" height="40" fill="white" stroke="#e5e7eb" stroke-width="1"/>
  <text x="30" y="92" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#6b7280">Settings > Notifications > SMS Gateway</text>
  
  <!-- Main Content -->
  <rect x="30" y="130" width="740" height="450" fill="white" filter="url(#shadow)" rx="8"/>
  
  <!-- Form Title -->
  <text x="50" y="165" font-family="Segoe UI, Arial, sans-serif" font-size="24" font-weight="700" fill="#111827">SMS Gateway Configuration</text>
  <text x="50" y="185" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#6b7280">Enter your SMS gateway credentials and settings</text>
  
  <!-- Form Fields -->
  <!-- Base URL Field -->
  <text x="50" y="220" font-family="Segoe UI, Arial, sans-serif" font-size="14" font-weight="600" fill="#374151">Base URL *</text>
  <rect x="50" y="230" width="350" height="40" fill="#f9fafb" stroke="#d1d5db" stroke-width="1" rx="6"/>
  <text x="60" y="252" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#6b7280">https://api.smsgateway.com/v1</text>
  
  <!-- API Key Field -->
  <text x="420" y="220" font-family="Segoe UI, Arial, sans-serif" font-size="14" font-weight="600" fill="#374151">API Key *</text>
  <rect x="420" y="230" width="320" height="40" fill="#f9fafb" stroke="#d1d5db" stroke-width="1" rx="6"/>
  <text x="430" y="252" font-family="Monaco, Consolas, monospace" font-size="14" fill="#6b7280">sk_live_••••••••••••••••</text>
  
  <!-- Account SID Field -->
  <text x="50" y="295" font-family="Segoe UI, Arial, sans-serif" font-size="14" font-weight="600" fill="#374151">Account SID *</text>
  <rect x="50" y="305" width="350" height="40" fill="#f9fafb" stroke="#d1d5db" stroke-width="1" rx="6"/>
  <text x="60" y="327" font-family="Monaco, Consolas, monospace" font-size="14" fill="#6b7280">AC1234567890abcdef123456</text>
  
  <!-- Auth Token Field -->
  <text x="420" y="295" font-family="Segoe UI, Arial, sans-serif" font-size="14" font-weight="600" fill="#374151">Auth Token *</text>
  <rect x="420" y="305" width="320" height="40" fill="#f9fafb" stroke="#d1d5db" stroke-width="1" rx="6"/>
  <text x="430" y="327" font-family="Monaco, Consolas, monospace" font-size="14" fill="#6b7280">••••••••••••••••••••••••</text>
  
  <!-- From Number Field -->
  <text x="50" y="370" font-family="Segoe UI, Arial, sans-serif" font-size="14" font-weight="600" fill="#374151">From Number *</text>
  <rect x="50" y="380" width="350" height="40" fill="#f9fafb" stroke="#d1d5db" stroke-width="1" rx="6"/>
  <text x="60" y="402" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#6b7280">+1234567890</text>
  
  <!-- Status Toggle -->
  <text x="420" y="370" font-family="Segoe UI, Arial, sans-serif" font-size="14" font-weight="600" fill="#374151">Enable SMS Gateway</text>
  <rect x="420" y="385" width="50" height="25" fill="#10b981" rx="12"/>
  <circle cx="445" cy="397.5" r="10" fill="white"/>
  <text x="480" y="402" font-family="Segoe UI, Arial, sans-serif" font-size="12" fill="#10b981">Enabled</text>
  
  <!-- Test Connection Section -->
  <rect x="50" y="440" width="690" height="80" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="1" rx="6"/>
  <text x="70" y="465" font-family="Segoe UI, Arial, sans-serif" font-size="16" font-weight="600" fill="#0c4a6e">Test Connection</text>
  <text x="70" y="485" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#0369a1">Send a test SMS to verify your configuration</text>
  <rect x="70" y="495" width="200" height="35" fill="#0ea5e9" rx="6"/>
  <text x="155" y="515" font-family="Segoe UI, Arial, sans-serif" font-size="14" font-weight="500" fill="white">Send Test SMS</text>
  
  <rect x="290" y="495" width="150" height="35" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
  <text x="300" y="515" font-family="Segoe UI, Arial, sans-serif" font-size="14" fill="#6b7280">+1234567890</text>
  
  <!-- Save Button -->
  <rect x="50" y="540" width="120" height="40" fill="#059669" rx="6"/>
  <text x="95" y="563" font-family="Segoe UI, Arial, sans-serif" font-size="14" font-weight="600" fill="white">Save Settings</text>
  
  <!-- Cancel Button -->
  <rect x="180" y="540" width="100" height="40" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
  <text x="215" y="563" font-family="Segoe UI, Arial, sans-serif" font-size="14" font-weight="500" fill="#6b7280">Cancel</text>
  
  <!-- Status Indicator -->
  <circle cx="680" cy="150" r="8" fill="#10b981"/>
  <text x="695" y="155" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="500" fill="#10b981">Connected</text>
</svg>
